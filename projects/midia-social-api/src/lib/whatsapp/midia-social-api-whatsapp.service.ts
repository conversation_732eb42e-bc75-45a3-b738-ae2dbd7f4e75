import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { MidiaSocialApiBaseService } from '../midia-social-api-base.service';
import { MidiaSocialApiModule } from '../midia-social-api.module';
import { Mensagem } from './midia-social-api-whatsapp.model';

@Injectable({
  providedIn: MidiaSocialApiModule,
})
export class MidiaSocialApiWhatsappService {
  constructor(private readonly restService: MidiaSocialApiBaseService) {}

  public obterAceite(): Observable<any> {
    return this.restService.get('v1/fila/whatsapp/aceite');
  }

  public obterMensagem(): Observable<any> {
    return this.restService.get('v1/fila/whatsapp/mensagem');
  }

  public enviarTemplate(template: Mensagem): Observable<any> {
    return this.restService.post('v1/fila/whatsapp', template);
  }
}
